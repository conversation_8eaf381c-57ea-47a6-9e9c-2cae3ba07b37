-- Migration: Add Certificate Versioning Support
-- Description: Adds versioning support to CertificateConfiguration and links TraineeCertificate to specific versions
-- Run this script in SQL Server Management Studio

BEGIN TRANSACTION;

PRINT 'Starting Certificate Versioning Migration...';

-- Step 1: Add new columns to CertificateConfiguration table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CertificateConfiguration') AND name = 'Version')
BEGIN
    ALTER TABLE CertificateConfiguration ADD Version INT NOT NULL DEFAULT 1;
    PRINT '✓ Added Version column to CertificateConfiguration';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CertificateConfiguration') AND name = 'ChangeReason')
BEGIN
    ALTER TABLE CertificateConfiguration ADD ChangeReason VARCHAR(250) NULL;
    PRINT '✓ Added ChangeReason column to CertificateConfiguration';
END

-- Step 2: Add new column to TraineeCertificate table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('TraineeCertificate') AND name = 'CertificateConfigurationId')
BEGIN
    ALTER TABLE TraineeCertificate ADD CertificateConfigurationId BIGINT NULL;
    PRINT '✓ Added CertificateConfigurationId column to TraineeCertificate';
END

-- Step 3: Create foreign key relationship (only if it doesn't exist)
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_TraineeCertificate_CertificateConfiguration')
BEGIN
    ALTER TABLE TraineeCertificate
    ADD CONSTRAINT FK_TraineeCertificate_CertificateConfiguration
    FOREIGN KEY (CertificateConfigurationId) REFERENCES CertificateConfiguration(Id);
    PRINT '✓ Created foreign key relationship';
END

-- Step 4: Update existing TraineeCertificate records to link with current CertificateConfiguration
UPDATE tc
SET CertificateConfigurationId = cc.Id
FROM TraineeCertificate tc
INNER JOIN CertificateConfiguration cc ON tc.CourseId = cc.CourseId
WHERE tc.CertificateConfigurationId IS NULL;

DECLARE @UpdatedRecords INT = @@ROWCOUNT;
PRINT '✓ Updated ' + CAST(@UpdatedRecords AS VARCHAR(10)) + ' existing TraineeCertificate records';

-- Step 5: Create indexes for better performance (only if they don't exist)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_TraineeCertificate_CertificateConfigurationId')
BEGIN
    CREATE INDEX IX_TraineeCertificate_CertificateConfigurationId
    ON TraineeCertificate (CertificateConfigurationId);
    PRINT '✓ Created index on TraineeCertificate.CertificateConfigurationId';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CertificateConfiguration_CourseId_Version')
BEGIN
    CREATE INDEX IX_CertificateConfiguration_CourseId_Version
    ON CertificateConfiguration (CourseId, Version);
    PRINT '✓ Created composite index on CertificateConfiguration (CourseId, Version)';
END

PRINT '';
PRINT 'Migration completed successfully! ✅';
PRINT '';
PRINT 'NEXT STEPS:';
PRINT '1. Run MigrateCertificateImages.ps1 to move existing images to v1 folders';
PRINT '2. Run UpdateCertificateImagePaths.sql to update database paths';
PRINT '3. Test certificate generation with existing data';

COMMIT TRANSACTION;
