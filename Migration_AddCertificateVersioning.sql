-- Migration: Add Certificate Versioning Support
-- Date: $(date)
-- Description: Adds versioning support to CertificateConfiguration and links TraineeCertificate to specific versions

BEGIN TRANSACTION;

-- Step 1: Add new columns to CertificateConfiguration table
ALTER TABLE CertificateConfiguration 
ADD Version INT NOT NULL DEFAULT 1,
    ChangeReason VARCHAR(250) NULL;

-- Step 2: Add new column to TraineeCertificate table
ALTER TABLE TraineeCertificate 
ADD CertificateConfigurationId BIGINT NULL;

-- Step 3: Create foreign key relationship
ALTER TABLE TraineeCertificate 
ADD CONSTRAINT FK_TraineeCertificate_CertificateConfiguration 
FOREIGN KEY (CertificateConfigurationId) REFERENCES CertificateConfiguration(Id);

-- Step 4: Update existing TraineeCertificate records to link with current CertificateConfiguration
UPDATE tc 
SET CertificateConfigurationId = cc.Id
FROM TraineeCertificate tc
INNER JOIN CertificateConfiguration cc ON tc.CourseId = cc.CourseId
WHERE tc.CertificateConfigurationId IS NULL;

-- Step 5: Create index for better performance
CREATE INDEX IX_TraineeCertificate_CertificateConfigurationId 
ON TraineeCertificate (CertificateConfigurationId);

CREATE INDEX IX_CertificateConfiguration_CourseId_Version 
ON CertificateConfiguration (CourseId, Version);

-- Step 6: Move existing certificate images to v1 folders
-- Note: This part needs to be done manually or via a separate script
-- as SQL cannot directly manipulate file system

PRINT 'Migration completed successfully!';
PRINT 'IMPORTANT: You need to manually move existing certificate images to version folders:';
PRINT '1. For each course folder in Images/CertificateConfiguration/{CourseId}/';
PRINT '2. Create a v1_' + CONVERT(VARCHAR, GETDATE(), 23) + '/ subfolder';
PRINT '3. Move all existing images to this subfolder';
PRINT '4. Update the file paths in CertificateConfiguration table if needed';

COMMIT TRANSACTION;
