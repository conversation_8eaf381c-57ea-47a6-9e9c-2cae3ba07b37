﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public class TraineeCertificate : AuditableEntity
    {
        public Guid TraineeId { get; set; }
        public virtual Trainee Trainee { get; set; }

        public Guid? TraineeExamId { get; set; }
        public virtual TraineeExam TraineeExam { get; set; }

        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }

        public int TotalMarks { get; set; }
        public float GainedMarks { get; set; }

        public Guid? GradingPolicyId { get; set; }
        public virtual GradingPolicy GradingPolicy { get; set; }

        public int GradingGroup { get; set; }
        public GradeResult? Result { get; set; }
        public string Grade { get; set; }
        public int GainedPercentage { get; set; }
        public int Attempts { get; set; }

        [Column(TypeName = "DATE")]
        public DateTime CertificateDate { get; set; }

        [Column(TypeName = "DATE")]
        public DateTime? ExpiryDate { get; set; }
        public bool Expired { get; set; }
        public int CNo { get; set; }    // Certificate number; This number will increase with each new achievement of a certificate for a specific course after expiry
    }
}
