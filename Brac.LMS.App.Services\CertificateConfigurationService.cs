﻿
using Brac.LMS.App.ViewModels;
using Brac.LMS.Common;
using Brac.LMS.DB;
using Brac.LMS.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;

namespace Brac.LMS.App.Services
{
    public class CertificateConfigurationService : ICertificateConfigurationService
    {
        private readonly ApplicationDbContext _context;
        private readonly AuditLogHelper _auditLogHelper;
        private readonly AuditLog audit;
        private readonly ApplicationUser _appUser;
        public CertificateConfigurationService()
        {
            _context = new ApplicationDbContext();
        }
        public CertificateConfigurationService(string controller, string method, ApplicationUser applicationUser)
        {
            _auditLogHelper = new AuditLogHelper(controller, method);
            audit = _auditLogHelper.auditLog;
            _appUser = applicationUser;
            audit.CreatedBy = _appUser.Id;
            _context = new ApplicationDbContext();
        }

        public async Task<APIResponse> CertificateConfigurationCreateOrUpdate(CertificateConfigurationModel model, IIdentity identity)
        {
            CertificateConfiguration item = null;
            bool isEdit = true;
            try
            {
                var test = HttpContext.Current.Request.Files.AllKeys;
                item = await _context.CertificateConfigurations.FirstOrDefaultAsync(x => x.CourseId == model.CourseId);

                if (item == null)
                {
                    item = new CertificateConfiguration { CourseId = model.CourseId };
                    isEdit = false;
                }

                item.CourseDescription = model.CourseDescription;
                item.Template = model.Template;
                item.Designation1 = model.Designation1;
                item.Designation2 = model.Designation2;
                item.Designation3 = model.Designation3;
                item.Person1Name = model.Person1Name;
                item.Person2Name = model.Person2Name;
                item.Person3Name = model.Person3Name;
                
                if (item.Person1Name == null && item.Person1SignPath != null)
                    item.Person1SignPath = Utility.RemoveImage(item.Person1SignPath);
                if (item.Person2Name == null && item.Person2SignPath != null)
                    item.Person2SignPath = Utility.RemoveImage(item.Person2SignPath);
                if (item.Person3Name == null && item.Person3SignPath != null)
                    item.Person3SignPath = Utility.RemoveImage(item.Person3SignPath);

                foreach (var fileKey in HttpContext.Current.Request.Files.AllKeys)
                {
                    switch (fileKey)
                    {
                        case "TemplatePath":
                            //if (item.TemplatePath != null && )
                            //    item.Person1SignPath = Utility.RemoveImage(item.TemplatePath);
                            item.TemplatePath = Utility.SaveImage("TemplatePath", "/Images/CertificateConfiguration/" + model.CourseId + "/", HttpContext.Current.Request.Files.Get(fileKey), item.TemplatePath);
                            break;
                        case "Person1Sign":
                            item.Person1SignPath = Utility.SaveImage("Person1Sign", "/Images/CertificateConfiguration/"+ model.CourseId+"/", HttpContext.Current.Request.Files.Get(fileKey), item.Person1SignPath);
                            break;
                        case "Person2Sign":
                            item.Person2SignPath = Utility.SaveImage("Person2Sign", "/Images/CertificateConfiguration/" + model.CourseId + "/", HttpContext.Current.Request.Files.Get(fileKey), item.Person2SignPath);
                            break;
                        case "Person3Sign":
                            item.Person3SignPath = Utility.SaveImage("Person3Sign", "/Images/CertificateConfiguration/" + model.CourseId + "/", HttpContext.Current.Request.Files.Get(fileKey), item.Person3SignPath);
                            break;
                        default:
                            return new APIResponse
                            {
                                Status = ResponseStatus.Warning,
                                Message = "Key not match"
                            };

                    };
                }

                item.SetAuditTrailEntity(identity);

                if (!isEdit)
                {
                    _context.CertificateConfigurations.Add(item);
                }
                else
                {
                    _context.Entry(item).State = EntityState.Modified;
                }
                await _context.SaveChangesAsync();
                await _auditLogHelper.AddSuccessAudit(audit, null, _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = new
                    {
                        Message = "Successfully " + (isEdit ? "Updated" : "Saved")
                    }
                };

            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(e), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = string.Join(" || ", e.EntityValidationErrors.SelectMany(x => x.ValidationErrors.Select(ve => ve.ErrorMessage).ToList()).ToList())
                };
            }
             catch (Exception ex)
            {
                await _auditLogHelper.AddErrorAudit(audit, JsonConvert.SerializeObject(ex), _context);
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }

        }

        public async Task<APIResponse> GetCertificateConfiguration(Guid courseId)
        {
            try
            {
                var data = await _context.CertificateConfigurations.Where(x => x.CourseId == courseId)
                .Select(t => new
                {
                    t.CourseDescription,
                    t.Designation1,
                    t.Designation2,
                    t.Designation3,
                    t.Person1Name,
                    t.Person2Name,
                    t.Person3Name,
                    t.Person1SignPath,
                    t.Person2SignPath,
                    t.Person3SignPath,
                    Template = t.Template.ToString(),
                    TemplatePath=t.TemplatePath
                }).FirstOrDefaultAsync();

                return new APIResponse
                {
                    Status = ResponseStatus.Success,
                    Data = data
                };

            }
            catch (Exception ex)
            {
                return new APIResponse
                {
                    Status = ResponseStatus.Error,
                    Message = ex.InnerException?.Message ?? ex.Message
                };
            }
        }
    }

    public interface ICertificateConfigurationService
    {
        Task<APIResponse> CertificateConfigurationCreateOrUpdate(CertificateConfigurationModel model, IIdentity identity);
        Task<APIResponse> GetCertificateConfiguration(Guid courseId);
    }
}
