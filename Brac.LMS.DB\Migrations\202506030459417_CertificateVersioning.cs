﻿namespace Brac.LMS.DB.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class CertificateVersioning : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.AuditLog",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        Controller = c.String(nullable: false, maxLength: 30),
                        Method = c.String(nullable: false, maxLength: 30),
                        CreatedBy = c.String(nullable: false),
                        CreatedOn = c.DateTime(nullable: false),
                        EndTime = c.DateTime(nullable: false),
                        Request = c.String(),
                        Response = c.String(),
                        Status = c.<PERSON>an(nullable: false),
                        StatusCode = c.String(nullable: false),
                        IsDelete = c<PERSON>(nullable: false),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.GhooriCertificate",
                c => new
                    {
                        Id = c.Guid(nullable: false),
                        CourseName = c.String(nullable: false, maxLength: 250),
                        TraineePIN = c.String(nullable: false, maxLength: 250),
                        TraineeName = c.String(nullable: false, maxLength: 250),
                        DateOfCertification = c.DateTime(),
                        CertficationType = c.String(nullable: false, maxLength: 250),
                        CertficationPath = c.String(nullable: false, maxLength: 250),
                        CreatedDate = c.DateTime(nullable: false),
                        CreatorId = c.String(nullable: false, maxLength: 128),
                        ModifiedDate = c.DateTime(),
                        ModifierId = c.String(maxLength: 128),
                    })
                .PrimaryKey(t => t.Id);
            
            CreateTable(
                "dbo.TraineeEvaluationActivity",
                c => new
                    {
                        Id = c.Long(nullable: false, identity: true),
                        TraineeId = c.Guid(nullable: false),
                        Title = c.String(),
                        NoOfContents = c.Int(nullable: false),
                        NoOfContentsCompleted = c.Int(nullable: false),
                        FirstStudyDate = c.DateTime(),
                        LastStudyDate = c.DateTime(),
                        VideoCompleted = c.Boolean(),
                        DocumentCompleted = c.Boolean(),
                        ExamCompleted = c.Boolean(),
                        Progress = c.Int(nullable: false),
                        ExamCompletedDate = c.DateTime(),
                        CreatedDate = c.DateTime(nullable: false),
                        CreatorId = c.String(nullable: false, maxLength: 128),
                        ModifiedDate = c.DateTime(),
                        ModifierId = c.String(maxLength: 128),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Trainee", t => t.TraineeId, cascadeDelete: true)
                .Index(t => t.TraineeId);
            
            AddColumn("dbo.CertificateConfiguration", "Version", c => c.Int(nullable: false));
            AddColumn("dbo.CertificateConfiguration", "ChangeReason", c => c.String(maxLength: 250, unicode: false));
            AddColumn("dbo.CertificateConfiguration", "TemplatePath", c => c.String(maxLength: 250, unicode: false));
            AddColumn("dbo.Course", "ShortTitle", c => c.String(nullable: false, maxLength: 250));
            AddColumn("dbo.TraineeEvaluationExamAttempt", "ExtendedQuota", c => c.Int(nullable: false));
            AddColumn("dbo.EvaluationExam", "Active", c => c.Boolean(nullable: false));
            AddColumn("dbo.EvaluationExam", "Order", c => c.Int());
            AddColumn("dbo.TraineeExamAttempt", "ExtendedQuota", c => c.Int(nullable: false));
            AddColumn("dbo.IdentityUser", "LastPasswordChanged", c => c.DateTime(nullable: false));
            AddColumn("dbo.OpenMaterial", "Active", c => c.Boolean(nullable: false));
            AddColumn("dbo.OpenMaterial", "Order", c => c.Int());
            AddColumn("dbo.Configuration", "DocumentationPath", c => c.String(maxLength: 250, unicode: false));
            AddColumn("dbo.Configuration", "InfoPath", c => c.String(maxLength: 250, unicode: false));
            AddColumn("dbo.TraineeExam", "Terminated", c => c.Boolean());
            AddColumn("dbo.TraineeEvaluationExam", "Terminated", c => c.Boolean());
            AddColumn("dbo.TraineeMockTest", "Status", c => c.Int(nullable: false));
            AddColumn("dbo.TraineeCertificate", "CertificateConfigurationId", c => c.Long());
            AlterColumn("dbo.CourseMaterial", "Title", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.CourseMaterial", "FilePath", c => c.String(maxLength: 250, unicode: false));
            AlterColumn("dbo.MockTestMCQQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.MockTestMCQQuestion", "Option1", c => c.String(nullable: false));
            AlterColumn("dbo.MockTestMCQQuestion", "Option2", c => c.String(nullable: false));
            AlterColumn("dbo.MockTestMCQQuestion", "Option3", c => c.String(nullable: false));
            AlterColumn("dbo.MockTestMCQQuestion", "Option4", c => c.String(nullable: false));
            AlterColumn("dbo.MockTestMCQQuestion", "Answers", c => c.String(nullable: false));
            AlterColumn("dbo.FIGEvaluationQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.FIGEvaluationQuestion", "Answer", c => c.String(nullable: false));
            AlterColumn("dbo.MatchingEvaluationQuestion", "LeftSide", c => c.String(nullable: false));
            AlterColumn("dbo.MatchingEvaluationQuestion", "RightSide", c => c.String(nullable: false));
            AlterColumn("dbo.MCQEvaluationQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option1", c => c.String(nullable: false));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option2", c => c.String(nullable: false));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option3", c => c.String(nullable: false));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option4", c => c.String(nullable: false));
            AlterColumn("dbo.MCQEvaluationQuestion", "Answers", c => c.String(nullable: false));
            AlterColumn("dbo.TrueFalseEvaluationQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.WrittenEvaluationQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.FIGQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.FIGQuestion", "Answer", c => c.String(nullable: false));
            AlterColumn("dbo.MatchingQuestion", "LeftSide", c => c.String(nullable: false));
            AlterColumn("dbo.MatchingQuestion", "RightSide", c => c.String(nullable: false));
            AlterColumn("dbo.MCQQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.MCQQuestion", "Option1", c => c.String(nullable: false));
            AlterColumn("dbo.MCQQuestion", "Option2", c => c.String(nullable: false));
            AlterColumn("dbo.MCQQuestion", "Option3", c => c.String(nullable: false));
            AlterColumn("dbo.MCQQuestion", "Option4", c => c.String(nullable: false));
            AlterColumn("dbo.MCQQuestion", "Answers", c => c.String(nullable: false));
            AlterColumn("dbo.TrueFalseQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.WrittenQuestion", "Question", c => c.String(nullable: false));
            AlterColumn("dbo.Configuration", "Address", c => c.String(nullable: false, maxLength: 1000, unicode: false));
            AlterColumn("dbo.Configuration", "InstituteWebsite", c => c.String(maxLength: 250, unicode: false));
            AlterColumn("dbo.Configuration", "InstituteEmail", c => c.String(maxLength: 250, unicode: false));
            AlterColumn("dbo.Configuration", "LogoPath", c => c.String(maxLength: 250, unicode: false));
            AlterColumn("dbo.Configuration", "AndoridAppLink", c => c.String(maxLength: 500, unicode: false));
            AlterColumn("dbo.Configuration", "iOSAppLink", c => c.String(maxLength: 500, unicode: false));
            AlterColumn("dbo.ExternalCourse", "Title", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.FIGAnswer", "Answered", c => c.String());
            AlterColumn("dbo.MatchingAnswer", "RightSide", c => c.String());
            AlterColumn("dbo.MCQAnswer", "Answered", c => c.String());
            AlterColumn("dbo.WrittenAnswer", "Answered", c => c.String());
            AlterColumn("dbo.FIGEvaluationAnswer", "Answered", c => c.String());
            AlterColumn("dbo.Library", "FilePath", c => c.String(nullable: false, maxLength: 250, unicode: false));
            AlterColumn("dbo.MatchingEvaluationAnswer", "RightSide", c => c.String());
            AlterColumn("dbo.MCQEvaluationAnswer", "Answered", c => c.String());
            AlterColumn("dbo.MCQMockTestAnswer", "Answered", c => c.String());
            AlterColumn("dbo.WrittenEvaluationAnswer", "Answered", c => c.String());
            CreateIndex("dbo.TraineeCertificate", "CertificateConfigurationId");
            AddForeignKey("dbo.TraineeCertificate", "CertificateConfigurationId", "dbo.CertificateConfiguration", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.TraineeEvaluationActivity", "TraineeId", "dbo.Trainee");
            DropForeignKey("dbo.TraineeCertificate", "CertificateConfigurationId", "dbo.CertificateConfiguration");
            DropIndex("dbo.TraineeEvaluationActivity", new[] { "TraineeId" });
            DropIndex("dbo.TraineeCertificate", new[] { "CertificateConfigurationId" });
            AlterColumn("dbo.WrittenEvaluationAnswer", "Answered", c => c.String(maxLength: 250));
            AlterColumn("dbo.MCQMockTestAnswer", "Answered", c => c.String(maxLength: 50));
            AlterColumn("dbo.MCQEvaluationAnswer", "Answered", c => c.String(maxLength: 50));
            AlterColumn("dbo.MatchingEvaluationAnswer", "RightSide", c => c.String(maxLength: 250));
            AlterColumn("dbo.Library", "FilePath", c => c.String(nullable: false, maxLength: 150, unicode: false));
            AlterColumn("dbo.FIGEvaluationAnswer", "Answered", c => c.String(maxLength: 250));
            AlterColumn("dbo.WrittenAnswer", "Answered", c => c.String(maxLength: 250));
            AlterColumn("dbo.MCQAnswer", "Answered", c => c.String(maxLength: 50));
            AlterColumn("dbo.MatchingAnswer", "RightSide", c => c.String(maxLength: 250));
            AlterColumn("dbo.FIGAnswer", "Answered", c => c.String(maxLength: 250));
            AlterColumn("dbo.ExternalCourse", "Title", c => c.String(nullable: false, maxLength: 250));
            AlterColumn("dbo.Configuration", "iOSAppLink", c => c.String(maxLength: 250, unicode: false));
            AlterColumn("dbo.Configuration", "AndoridAppLink", c => c.String(maxLength: 250, unicode: false));
            AlterColumn("dbo.Configuration", "LogoPath", c => c.String(maxLength: 100, unicode: false));
            AlterColumn("dbo.Configuration", "InstituteEmail", c => c.String(maxLength: 50, unicode: false));
            AlterColumn("dbo.Configuration", "InstituteWebsite", c => c.String(maxLength: 100, unicode: false));
            AlterColumn("dbo.Configuration", "Address", c => c.String(nullable: false, maxLength: 500, unicode: false));
            AlterColumn("dbo.WrittenQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.TrueFalseQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQQuestion", "Answers", c => c.String(nullable: false, maxLength: 50));
            AlterColumn("dbo.MCQQuestion", "Option4", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQQuestion", "Option3", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQQuestion", "Option2", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQQuestion", "Option1", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MatchingQuestion", "RightSide", c => c.String(nullable: false, maxLength: 250));
            AlterColumn("dbo.MatchingQuestion", "LeftSide", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.FIGQuestion", "Answer", c => c.String(nullable: false, maxLength: 250));
            AlterColumn("dbo.FIGQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.WrittenEvaluationQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.TrueFalseEvaluationQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQEvaluationQuestion", "Answers", c => c.String(nullable: false, maxLength: 50));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option4", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option3", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option2", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQEvaluationQuestion", "Option1", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MCQEvaluationQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MatchingEvaluationQuestion", "RightSide", c => c.String(nullable: false, maxLength: 250));
            AlterColumn("dbo.MatchingEvaluationQuestion", "LeftSide", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.FIGEvaluationQuestion", "Answer", c => c.String(nullable: false, maxLength: 250));
            AlterColumn("dbo.FIGEvaluationQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MockTestMCQQuestion", "Answers", c => c.String(nullable: false, maxLength: 50));
            AlterColumn("dbo.MockTestMCQQuestion", "Option4", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MockTestMCQQuestion", "Option3", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MockTestMCQQuestion", "Option2", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MockTestMCQQuestion", "Option1", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.MockTestMCQQuestion", "Question", c => c.String(nullable: false, maxLength: 500));
            AlterColumn("dbo.CourseMaterial", "FilePath", c => c.String(maxLength: 150, unicode: false));
            AlterColumn("dbo.CourseMaterial", "Title", c => c.String(nullable: false, maxLength: 250));
            DropColumn("dbo.TraineeCertificate", "CertificateConfigurationId");
            DropColumn("dbo.TraineeMockTest", "Status");
            DropColumn("dbo.TraineeEvaluationExam", "Terminated");
            DropColumn("dbo.TraineeExam", "Terminated");
            DropColumn("dbo.Configuration", "InfoPath");
            DropColumn("dbo.Configuration", "DocumentationPath");
            DropColumn("dbo.OpenMaterial", "Order");
            DropColumn("dbo.OpenMaterial", "Active");
            DropColumn("dbo.IdentityUser", "LastPasswordChanged");
            DropColumn("dbo.TraineeExamAttempt", "ExtendedQuota");
            DropColumn("dbo.EvaluationExam", "Order");
            DropColumn("dbo.EvaluationExam", "Active");
            DropColumn("dbo.TraineeEvaluationExamAttempt", "ExtendedQuota");
            DropColumn("dbo.Course", "ShortTitle");
            DropColumn("dbo.CertificateConfiguration", "TemplatePath");
            DropColumn("dbo.CertificateConfiguration", "ChangeReason");
            DropColumn("dbo.CertificateConfiguration", "Version");
            DropTable("dbo.TraineeEvaluationActivity");
            DropTable("dbo.GhooriCertificate");
            DropTable("dbo.AuditLog");
        }
    }
}
