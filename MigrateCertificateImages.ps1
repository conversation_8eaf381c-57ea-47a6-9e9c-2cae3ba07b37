# PowerShell Script to migrate existing certificate images to version folders
# Run this script from the API project root directory

param(
    [string]$ImagePath = "Images\CertificateConfiguration",
    [string]$BackupPath = "Images\CertificateConfiguration_Backup"
)

Write-Host "Starting Certificate Image Migration..." -ForegroundColor Green

# Create backup directory
if (!(Test-Path $BackupPath)) {
    New-Item -ItemType Directory -Path $BackupPath -Force
    Write-Host "Created backup directory: $BackupPath" -ForegroundColor Yellow
}

# Get current date for version folder naming
$currentDate = Get-Date -Format "yyyy-MM-dd"
$versionFolder = "v1_$currentDate"

# Get all course directories
$courseDirs = Get-ChildItem -Path $ImagePath -Directory | Where-Object { $_.Name -match '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$' }

Write-Host "Found $($courseDirs.Count) course directories to migrate" -ForegroundColor Cyan

foreach ($courseDir in $courseDirs) {
    $coursePath = $courseDir.FullName
    $courseId = $courseDir.Name
    
    Write-Host "Processing course: $courseId" -ForegroundColor White
    
    # Create backup of this course directory
    $backupCoursePath = Join-Path $BackupPath $courseId
    Copy-Item -Path $coursePath -Destination $backupCoursePath -Recurse -Force
    Write-Host "  ✓ Backup created" -ForegroundColor Green
    
    # Get all image files in the course directory (not in subdirectories)
    $imageFiles = Get-ChildItem -Path $coursePath -File | Where-Object { 
        $_.Extension -match '\.(jpg|jpeg|png|gif|bmp)$' 
    }
    
    if ($imageFiles.Count -gt 0) {
        # Create version directory
        $versionPath = Join-Path $coursePath $versionFolder
        if (!(Test-Path $versionPath)) {
            New-Item -ItemType Directory -Path $versionPath -Force
            Write-Host "  ✓ Created version directory: $versionFolder" -ForegroundColor Green
        }
        
        # Move image files to version directory
        foreach ($imageFile in $imageFiles) {
            $destinationPath = Join-Path $versionPath $imageFile.Name
            Move-Item -Path $imageFile.FullName -Destination $destinationPath -Force
            Write-Host "  ✓ Moved: $($imageFile.Name)" -ForegroundColor Gray
        }
        
        Write-Host "  ✓ Migrated $($imageFiles.Count) images to $versionFolder" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ No images found in root directory" -ForegroundColor Yellow
    }
}

Write-Host "`nMigration completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run the SQL migration script in Visual Studio" -ForegroundColor White
Write-Host "2. Update CertificateConfiguration records to use new version paths" -ForegroundColor White
Write-Host "3. Test certificate generation with existing data" -ForegroundColor White
Write-Host "`nBackup location: $BackupPath" -ForegroundColor Yellow
