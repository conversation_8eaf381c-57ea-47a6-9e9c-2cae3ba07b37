namespace Brac.LMS.DB.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddCertificateVersioning : DbMigration
    {
        public override void Up()
        {
            // Add Version column to CertificateConfiguration
            AddColumn("dbo.CertificateConfiguration", "Version", c => c.Int(nullable: false, defaultValue: 1));
            
            // Add ChangeReason column to CertificateConfiguration
            AddColumn("dbo.CertificateConfiguration", "ChangeReason", c => c.String(maxLength: 250, unicode: false));
            
            // Add CertificateConfigurationId column to TraineeCertificate
            AddColumn("dbo.TraineeCertificate", "CertificateConfigurationId", c => c.<PERSON>());
            
            // Create foreign key relationship
            CreateIndex("dbo.TraineeCertificate", "CertificateConfigurationId");
            AddForeignKey("dbo.TraineeCertificate", "CertificateConfigurationId", "dbo.CertificateConfiguration", "Id");
            
            // Update existing TraineeCertificate records to link with current CertificateConfiguration
            Sql(@"
                UPDATE tc 
                SET CertificateConfigurationId = cc.Id
                FROM TraineeCertificate tc
                INNER JOIN CertificateConfiguration cc ON tc.CourseId = cc.CourseId
                WHERE tc.CertificateConfigurationId IS NULL
            ");
            
            // Create additional indexes for performance
            CreateIndex("dbo.CertificateConfiguration", new[] { "CourseId", "Version" }, name: "IX_CertificateConfiguration_CourseId_Version");
        }
        
        public override void Down()
        {
            // Drop indexes
            DropIndex("dbo.CertificateConfiguration", "IX_CertificateConfiguration_CourseId_Version");
            DropIndex("dbo.TraineeCertificate", new[] { "CertificateConfigurationId" });
            
            // Drop foreign key
            DropForeignKey("dbo.TraineeCertificate", "CertificateConfigurationId", "dbo.CertificateConfiguration");
            
            // Drop columns
            DropColumn("dbo.TraineeCertificate", "CertificateConfigurationId");
            DropColumn("dbo.CertificateConfiguration", "ChangeReason");
            DropColumn("dbo.CertificateConfiguration", "Version");
        }
    }
}
