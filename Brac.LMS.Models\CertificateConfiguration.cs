﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Brac.LMS.Models
{
    public enum CertificateTemplate { Template_1 }
    public class CertificateConfiguration : NumberAuditableEntity
    {
        [Required]
        public Guid CourseId { get; set; }
        public virtual Course Course { get; set; }


        [Required, Column(TypeName = "VARCHAR"), StringLength(500)]
        public string CourseDescription { get; set; }



        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Designation1 { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Person1Name { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Person1SignPath { get; set; }



        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Designation2 { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Person2Name { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Person2SignPath { get; set; }



        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Designation3 { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Person3Name { get; set; }


        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string Person3SignPath { get; set; }

        public CertificateTemplate Template { get; set; }

        [Column(TypeName = "VARCHAR"), StringLength(250)]
        public string TemplatePath { get; set; }
        
    }
}
