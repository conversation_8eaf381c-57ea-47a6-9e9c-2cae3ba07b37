<block-ui>
  <div class="card card-border-primary">
    <div class="card-body">
      <form [formGroup]="entryForm" autocomplete="off">
        <div class="mb-3 row ">
          <div class="col-lg-6 col-md-6 col-12">
            <div class="mb-3 row">
              <label class="col-lg-4 col-md-4 col-12 col-form-label col-form-label-sm fw-bold required">
                Course
              </label>
              <div class="col-lg-8 col-md-8 col-12">
                <div class="d-flex align-items-center gap-2">
                  <ng-select #selectElement (click)="handleSelectClick(selectElement)" *ngIf="courseList.length > 0"
                    class="form-control form-control-sm flex-grow-1" formControlName="course" [clearable]="false"
                    [clearOnBackspace]="false" [items]="courseList" (change)="getItem($event.Id)" bindLabel="Title"
                    bindValue="Id" placeholder="Select a course">
                  </ng-select>
                  <span *ngIf="btnSaveText === 'Update'" class="badge bg-info text-white">v{{currentVersion}}</span>
                </div>
                <div *ngIf="submitted && f.course.errors" class="error-text">
                  <span *ngIf="f.course.errors.required" class="text-danger"> Course is
                    required</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-6 col-md-6 col-12">
            <div class="mb-3 row">
              <label class="col-lg-2 col-md-4 col-12  col-form-label col-form-label-sm fw-bold required">Template
              </label>
              <div class="col-lg-10 col-md-8 col-12 ">
                <ng-select #selectElementT (click)="handleSelectClick(selectElementT)" formControlName="template"
                  class="form-control form-control-sm" [ngClass]="{ 'is-invalid': submitted && f.template.errors }"
                  [clearable]="false" [clearOnBackspace]="false" [items]="templateList" bindLabel="Name" bindValue="Id"
                  placeholder="Select">
                </ng-select>
                <div *ngIf="submitted && f.template.errors" class="error-text">
                  <span *ngIf="f.template.errors.required" class="text-danger">Template is
                    required</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mb-3 row">
          <div class="col-12">
            <div class="mb-3 row">
              <label class="col-lg-2 col-md-3 col-12 col-form-label col-form-label-sm fw-bold required">Course
                Description
              </label>
              <div class="col-lg-10 col-md-9 col-12">
                <textarea type="text" formControlName="courseDescription"
                  placeholder="e.g. has participated in an online course on" rows="1"
                  class="form-control form-control-sm"> </textarea>

                <div *ngIf="submitted && f.courseDescription.errors" class="error-text">
                  <span *ngIf="f.courseDescription.errors.required" class="text-danger">Course Description is
                    required</span>
                  <span *ngIf="f.courseDescription.errors.maxLength" class="text-danger">Course Description cannot be
                    longer than 500 characters</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mb-3 row">
          <label class="col-12 col-form-label col-form-label-sm fw-bold">Certificate Image Background</label>
          <div class="col-12">
            <div class="position-relative d-inline-block" *ngIf="templateImageURL">
              <img [src]="templateImageURL" class="img-thumbnail"
                style="max-width: 400px; max-height: 300px; object-fit: contain;">
              <button class="btn btn-danger btn-sm position-absolute top-0 end-0" (click)="removeImage(0)"
                style="transform: translate(50%, -50%);">
                <i class="fa fa-times"></i>
              </button>
            </div>
            <input class="mt-2 w-100" #templateImagePath type="file" formControlName="templateImagePath"
              accept='image/png,image/jpeg' (change)="templatePreview(templateImagePath.files)" />
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <p class="card-text alert alert-danger f-16 p-1 fw-bold text-center text-uppercase">
              <i class="fa fa-alert"></i> Minimum one signatory is mandatory
            </p>
          </div>
          <div class="col-lg-4 col-md-6 col-12">
            <div class="card border-theme w-100">
              <div class="card-header pt-3 pb-3 text-center">
                <h5 class="card-title">Signatory 1</h5>
              </div>
              <div class="card-body pt-2 pb-2">
                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Name </label>
                  <div class="col-12">
                    <input type="text" formControlName="person1Name"
                      [ngClass]="{ 'is-invalid': submitted && f.person1Name.errors }" placeholder="Enter name"
                      class="form-control form-control-sm">
                    <div *ngIf="submitted && f.person1Name.errors" class="error-text">
                      <span *ngIf="f.person1Name.errors.maxLength" class="text-danger">
                        Name cannot be longer than 250 characters
                      </span>
                    </div>
                  </div>
                </div>

                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Designation </label>
                  <div class="col-12">
                    <input type="text" formControlName="designation1"
                      [ngClass]="{ 'is-invalid': submitted && f.designation1.errors }" placeholder="Enter designation"
                      class="form-control form-control-sm">

                    <div *ngIf="submitted && f.designation1.errors" class="error-text">
                      <span *ngIf="f.designation1.errors.maxLength" class="text-danger">
                        Designation cannot be longer than 250 characters
                      </span>
                    </div>
                  </div>
                </div>

                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Signature Image (255x47) </label>
                  <div class="col-12">
                    <div class="position-relative d-inline-block" *ngIf="person1SignURL">
                      <img [src]="person1SignURL" class="img-thumbnail"
                        style="width: 255px; height: 47px; object-fit: contain;">
                      <button class="btn btn-danger btn-sm position-absolute top-0 end-0" (click)="removeImage(1)"
                        style="transform: translate(50%, -50%);">
                        <i class="fa fa-times"></i>
                      </button>
                    </div>
                    <input class="mt-2 w-100" #person1SignPath type="file" formControlName="person1SignPath"
                      accept='image/png,image/jpeg' (change)="person1SignPreview(person1SignPath.files)" />
                  </div>
                </div>

              </div>
            </div>

          </div>

          <div class="col-lg-4 col-md-6 col-12">
            <div class="card border-theme w-100">
              <div class="card-header pt-3 pb-3 text-center">
                <h5 class="card-title">Signatory 2</h5>
              </div>
              <div class="card-body pt-2 pb-2">
                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Name </label>
                  <div class="col-12">
                    <input type="text" formControlName="person2Name"
                      [ngClass]="{ 'is-invalid': submitted && f.person2Name.errors }" placeholder="Enter name"
                      class="form-control form-control-sm">
                    <div *ngIf="submitted && f.person2Name.errors" class="error-text">
                      <span *ngIf="f.person2Name.errors.maxLength" class="text-danger">
                        Name cannot be longer than 250 characters
                      </span>
                    </div>

                  </div>
                </div>

                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Designation </label>
                  <div class="col-12">
                    <input type="text" formControlName="designation2"
                      [ngClass]="{ 'is-invalid': submitted && f.designation2.errors }" placeholder="Enter designation"
                      class="form-control form-control-sm">

                    <div *ngIf="submitted && f.designation2.errors" class="error-text">
                      <span *ngIf="f.designation2.errors.maxLength" class="text-danger">
                        Designation cannot be longer than 250 characters
                      </span>
                    </div>
                  </div>
                </div>

                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Signature Image (255x47) </label>
                  <div class="col-12">
                    <div class="position-relative d-inline-block" *ngIf="person2SignURL">
                      <img [src]="person2SignURL" class="img-thumbnail"
                        style="width: 255px; height: 47px; object-fit: contain;">
                      <button class="btn btn-danger btn-sm position-absolute top-0 end-0" (click)="removeImage(2)"
                        style="transform: translate(50%, -50%);">
                        <i class="fa fa-times"></i>
                      </button>
                    </div>
                    <input class="mt-2 w-100" #person2SignPath type="file" formControlName="person2SignPath"
                      accept='image/png,image/jpeg' (change)="person2SignPreview(person2SignPath.files)" />
                  </div>
                </div>

              </div>
            </div>

          </div>


          <div class="col-lg-4 col-md-6 col-12">
            <div class="card border-theme w-100">
              <div class="card-header pt-3 pb-3 text-center">
                <h5 class="card-title">Signatory 3</h5>
              </div>
              <div class="card-body pt-2 pb-2">
                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Name </label>
                  <div class="col-12">
                    <input type="text" formControlName="person3Name"
                      [ngClass]="{ 'is-invalid': submitted && f.person3Name.errors }" placeholder="Enter name"
                      class="form-control form-control-sm">
                    <div *ngIf="submitted && f.person3Name.errors" class="error-text">
                      <span *ngIf="f.person3Name.errors.maxLength" class="text-danger">
                        Name cannot be longer than 250 characters
                      </span>
                    </div>
                  </div>
                </div>

                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Designation </label>
                  <div class="col-12">
                    <input type="text" formControlName="designation3"
                      [ngClass]="{ 'is-invalid': submitted && f.designation3.errors }" placeholder="Enter designation"
                      class="form-control form-control-sm">

                    <div *ngIf="submitted && f.designation3.errors" class="error-text">
                      <span *ngIf="f.designation3.errors.maxLength" class="text-danger">
                        Designation cannot be longer than 250 characters
                      </span>
                    </div>
                  </div>
                </div>

                <div class="mb-3 row">
                  <label class="col-12 col-form-label col-form-label-sm fw-bold">Signature Image (255x47) </label>
                  <div class="col-12">
                    <div class="position-relative d-inline-block" *ngIf="person3SignURL">
                      <img [src]="person3SignURL" class="img-thumbnail"
                        style="width: 255px; height: 47px; object-fit: contain;">
                      <button class="btn btn-danger btn-sm position-absolute top-0 end-0" (click)="removeImage(3)"
                        style="transform: translate(50%, -50%);">
                        <i class="fa fa-times"></i>
                      </button>
                    </div>
                    <input class="mt-2 w-100" #person3SignPath type="file" formControlName="person3SignPath"
                      accept='image/png,image/jpeg' (change)="person3SignPreview(person3SignPath.files)" />
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>

        <div class="row">
          <div class="col-12 mt-2 text-center">
            <button class="btn btn-theme btn-testz wid-150" (click)="onFormSubmit()">
              <i class="feather icon-save"></i> {{btnSaveText}}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</block-ui>

<!-- Version Choice Modal -->
<ng-template #versionModal>
  <div class="modal-header">
    <h4 class="modal-title">Save Certificate Configuration</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="closeVersionModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="mb-3">
      <p class="text-muted mb-3">How would you like to save your changes?</p>

      <div class="form-check mb-3">
        <input class="form-check-input" type="radio" name="versionChoice" id="updateCurrent" [value]="false"
          [(ngModel)]="createNewVersion" (change)="onVersionChoiceChange(false)">
        <label class="form-check-label" for="updateCurrent">
          <strong>Update current version (v{{currentVersion}})</strong>
          <br>
          <small class="text-muted">
            Replaces the existing configuration. All future certificate downloads will use the updated version.
            <br><strong>Note:</strong> This affects certificates for all trainees.
          </small>
        </label>
      </div>

      <div class="form-check mb-3">
        <input class="form-check-input" type="radio" name="versionChoice" id="createNew" [value]="true"
          [(ngModel)]="createNewVersion" (change)="onVersionChoiceChange(true)">
        <label class="form-check-label" for="createNew">
          <strong>Create new version (v{{currentVersion + 1}})</strong>
          <br>
          <small class="text-muted">
            Preserves historical accuracy. Existing certificates keep their original signatures.
            <br><strong>Note:</strong> Only new certificates will use the updated version.
          </small>
        </label>
      </div>

      <div *ngIf="createNewVersion" class="mt-3">
        <label class="form-label fw-bold">Reason for new version <span class="text-danger">*</span></label>
        <input type="text" class="form-control form-control-sm" [(ngModel)]="changeReason"
          placeholder="e.g., Updated signatures, New management" maxlength="50">
        <small class="text-muted">Maximum 50 characters ({{changeReason.length}}/50)</small>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeVersionModal()">
      <i class="fa fa-times"></i> Cancel
    </button>
    <button type="button" class="btn btn-theme" (click)="confirmSave()">
      <i class="feather icon-save"></i>
      {{createNewVersion ? 'Create New Version' : 'Update Current'}}
    </button>
  </div>
</ng-template>