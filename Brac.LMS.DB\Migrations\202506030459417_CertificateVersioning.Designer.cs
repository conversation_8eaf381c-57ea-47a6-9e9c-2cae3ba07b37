﻿// <auto-generated />
namespace Brac.LMS.DB.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.4.4")]
    public sealed partial class CertificateVersioning : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(CertificateVersioning));
        
        string IMigrationMetadata.Id
        {
            get { return "202506030459417_CertificateVersioning"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return Resources.GetString("Source"); }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
